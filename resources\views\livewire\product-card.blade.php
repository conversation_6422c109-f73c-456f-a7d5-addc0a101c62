<div class="bg-white border border-gray-200 rounded-lg shadow-md group overflow-hidden">
    <a href="{{ route('products.show', $product->slug) }}" class="block relative">
        @if ($product->image_url)
            <img src="{{ $product->image_url }}" alt="{{ $product->name }}" class="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-300">
        @else
            <img src="{{ asset('storage/product-placeholder.png') }}" alt="{{ $product->name }}" class="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-300">
        @endif

        @if ($product->is_on_sale)
            <span class="absolute top-2 right-2 bg-black text-white text-xs font-bold px-2 py-1 rounded">SALE</span>
        @endif
    </a>
    <div class="p-4">
        <h3 class="text-lg font-semibold mb-2 truncate">
            <a href="{{ route('products.show', $product->slug) }}" class="hover:text-black transition-colors">
                {{ $product->name }}
            </a>
        </h3>
        @if ($product->brand)
            <p class="text-sm text-gray-500 mb-2">{{ $product->brand->name }}</p>
        @endif
        <div class="flex justify-between items-center mb-3">
            @if ($product->is_on_sale && $product->discount_price)
                <div>
                    <span class="text-xl font-bold text-black">₦{{ number_format($product->discount_price) }}</span>
                    <span class="text-sm text-gray-500 line-through ml-2">₦{{ number_format($product->price) }}</span>
                </div>
            @else
                <span class="text-xl font-bold text-black">₦{{ number_format($product->price) }}</span>
            @endif
        </div>
        <div class="flex items-center space-x-2">
            <button wire:click="addToCart" wire:loading.attr="disabled" wire:target="addToCart" class="w-full bg-black text-white px-4 py-2 rounded-full hover:bg-gray-800 transition-colors flex items-center justify-center">
                <span wire:loading.remove wire:target="addToCart" class="flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                    Add to Cart
                </span>
                <span wire:loading wire:target="addToCart" class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Adding...
                </span>
            </button>
            <button wire:click="addToWishlist" wire:loading.attr="disabled" wire:target="addToWishlist" class="bg-black text-white hover:bg-gray-800 transition-colors p-2 rounded-full">
                <span wire:loading.remove wire:target="addToWishlist">
                    @if ($inWishlist)
                        <x-heroicon-s-heart class="w-5 h-5 text-red-500" />
                    @else
                        <x-heroicon-o-heart class="w-5 h-5" />
                    @endif
                </span>
                <span wire:loading wire:target="addToWishlist">
                    <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </span>
            </button>
        </div>
    </div>
</div>

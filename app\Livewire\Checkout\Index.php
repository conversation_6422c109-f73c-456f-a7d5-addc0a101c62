<?php

namespace App\Livewire\Checkout;

use App\Helpers\Location;
use App\Models\User;
use App\Services\ShipBubbleService;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.app')]
class Index extends Component
{
    public $cartItems = [];
    public $subtotal = 0;

    public $shippingAddress = [
        'address' => '',
        'city' => '',
        'lga' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'NG',
    ];

    public $states = [];
    public $lgas = [];

    public $shippingRates = [];
    public $selectedShippingRate = null;
    public $shippingCost = 0;
    public $total = 0;

    protected $rules = [
        'shippingAddress.address' => 'required|string|max:255',
        'shippingAddress.city' => 'required|string|max:255',
        'shippingAddress.state' => 'required|string|exists:locations,state',
        'shippingAddress.lga' => 'required|string',
        'shippingAddress.postal_code' => 'nullable|string|max:20',
    ];

    public function mount()
    {
        $this->cartItems = session('cart', []);
        if (empty($this->cartItems)) {
            return redirect()->route('cart.index');
        }

        $this->calculateSubtotal();

        /** @var User $user */
        $user = Auth::user();
        if ($user && $user->shippingAddress) {
            $this->shippingAddress = $user->shippingAddress->toArray();
        }

        $this->states = Location::getStates();
        if (!empty($this->shippingAddress['state'])) {
            $this->lgas = Location::getLgas($this->shippingAddress['state']);
        }

        $this->calculateTotal();
    }

    public function updatedShippingAddressState($state)
    {
        $this->lgas = Location::getLgas($state);
        $this->shippingAddress['lga'] = ''; // Reset LGA on state change
        $this->resetShipping();
    }

    public function updatedShippingAddressLga()
    {
        $this->resetShipping();
    }

    public function getShippingRates()
    {
        $this->validate();
        $this->resetShipping();

        try {
            $shipBubble = new ShipBubbleService();
            $rates = $shipBubble->getShippingRates($this->cartItems, $this->shippingAddress);
            $this->shippingRates = $rates;
        } catch (\Exception $e) {
            $this->addError('shipping', 'Could not retrieve shipping rates. Please check your address and try again.');
        }
    }

    public function selectShippingRate($rateKey)
    {
        if (isset($this->shippingRates[$rateKey])) {
            $this->selectedShippingRate = $this->shippingRates[$rateKey];
            $this->shippingCost = $this->selectedShippingRate['fee'];
            $this->calculateTotal();
        }
    }

    private function calculateSubtotal()
    {
        $this->subtotal = collect($this->cartItems)->sum(function ($item) {
            return $item['price'] * $item['quantity'];
        });
    }

    private function calculateTotal()
    {
        $this->total = $this->subtotal + $this->shippingCost;
    }

    private function resetShipping()
    {
        $this->shippingRates = [];
        $this->selectedShippingRate = null;
        $this->shippingCost = 0;
        $this->calculateTotal();
    }

    public function render()
    {
        return view('livewire.checkout.index');
    }
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShipBubbleService
{
    protected $apiKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.shipbubble.key');
        $this->baseUrl = config('services.shipbubble.url');
    }

    public function getRates(array $data)
    {
        return $this->sendRequest('post', '/shipping/fetch_rates', $data);
    }

    public function createShipment(array $data)
    {
        return $this->sendRequest('post', '/shipping/labels', $data);
    }

    public function validateAddress(array $data)
    {
        // Build a simple, clean address string using only the essential components.
        $addressParts = [
            $data['address'] ?? null,
            $data['city'] ?? null,
            $data['state'] ?? null,
        ];

        // Filter out any null or empty parts.
        $filteredParts = array_filter($addressParts);

        // Join the parts into a single, comma-separated string.
        $addressString = implode(', ', $filteredParts);

        // Append the country for clarity, as the API seems to require it.
        $addressString .= ', Nigeria';

        // Sanitize the final address string to remove any characters the API might reject.
        $sanitizedAddress = preg_replace('/[^\p{L}\p{N}\s,]/u', '', $addressString);

        // Final cleanup to remove potential duplicate commas or leading/trailing commas/spaces.
        $sanitizedAddress = trim(preg_replace('/,{2,}/', ',', $sanitizedAddress), ' ,');

        // Construct the final payload with the clean address.
        $payload = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'address' => $sanitizedAddress,
        ];

        return $this->sendRequest('post', '/shipping/address/validate', $payload);
    }

    protected function sendRequest(string $method, string $endpoint, array $data = [])
    {
        Log::info('ShipBubble API Request:', [
            'endpoint' => $endpoint,
            'payload' => $data
        ]);

        try {
            $response = Http::withToken($this->apiKey)
                ->acceptJson()
                ->{$method}($this->baseUrl . $endpoint, $data);

            if ($response->failed()) {
                Log::error('ShipBubble API Error:', [
                    'endpoint' => $endpoint,
                    'status' => $response->status(),
                    'response' => $response->json() ?? $response->body()
                ]);
                return [
                    'status' => 'error',
                    'message' => 'ShipBubble API request failed.',
                    'details' => $response->json() ?? $response->body(),
                ];
            }

            Log::info('ShipBubble API Response:', [
                'endpoint' => $endpoint,
                'response' => $response->json()
            ]);

            return $response->json();

        } catch (\Exception $e) {
            Log::critical('ShipBubble Service Exception:', [
                'endpoint' => $endpoint,
                'message' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'An unexpected error occurred.',
                'details' => $e->getMessage()
            ];
        }
    }
}

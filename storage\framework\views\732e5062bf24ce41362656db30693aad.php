<div>
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Earnings & Withdrawals</h1>
    </div>

    <!--[if BLOCK]><![endif]--><?php if(session()->has('success')): ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
            <p><?php echo e(session('success')); ?></p>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php if(session()->has('error')): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <p><?php echo e(session('error')); ?></p>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h6 class="text-gray-500 mb-1">Total Balance</h6>
            <h3 class="text-2xl font-bold">₦<?php echo e(number_format($balance, 2)); ?></h3>
            <p class="text-gray-400 text-sm mt-2">Your total account balance from all transactions.</p>
        </div>
        <div class="bg-green-50 p-6 rounded-lg shadow-md">
            <h6 class="text-green-700 mb-1">Withdrawable Balance</h6>
            <h3 class="text-2xl font-bold text-green-800">₦<?php echo e(number_format($withdrawableBalance, 2)); ?></h3>
            <div class="mt-4">
                <button x-data @click="$dispatch('open-modal')" class="w-full bg-black text-white font-bold py-2 px-4 rounded hover:bg-gray-800 transition duration-300">
                    Withdraw Funds
                </button>
            </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h6 class="text-gray-500 mb-1">Total Withdrawn</h6>
            <h3 class="text-2xl font-bold">₦<?php echo e(number_format($totalWithdrawn, 2)); ?></h3>
            <p class="text-gray-400 text-sm mt-2">Total funds successfully withdrawn.</p>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b">
            <h5 class="font-bold text-lg">Transaction History</h5>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full leading-normal">
                <thead>
                    <tr class="border-b bg-gray-50">
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                        <th class="px-5 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                        <th class="px-5 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
                        <th class="px-5 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Balance</th>
                    </tr>
                </thead>
                <tbody>
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-5 py-5 border-b border-gray-200 text-sm"><?php echo e($transaction->created_at->format('M d, Y H:i')); ?></td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($transaction->amount > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?> text-capitalize">
                                    <?php echo e($transaction->type); ?>

                                </span>
                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm"><?php echo e($transaction->description); ?></td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm text-right font-semibold <?php echo e($transaction->amount < 0 ? 'text-red-600' : 'text-green-600'); ?>">
                                ₦<?php echo e(number_format(abs($transaction->amount), 2)); ?>

                            </td>
                            <td class="px-5 py-5 border-b border-gray-200 text-sm text-right">₦<?php echo e(number_format($transaction->balance_after, 2)); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center py-10 text-gray-500">No transactions found.</td>
                        </tr>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4">
            <?php echo e($transactions->links()); ?>

        </div>
    </div>

    <!-- Withdrawal Modal -->
    <div x-data="{ show: false }" @open-modal.window="show = true" @close-modal.window="show = false" x-show="show" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto" @click.away="show = false">
            <form wire:submit.prevent="withdraw">
                <h5 class="text-lg font-bold mb-4">Request Bank Transfer</h5>
                <div class="bg-blue-50 border border-blue-200 text-blue-800 text-sm p-3 rounded mb-4">
                    Withdrawals are processed via bank transfer to Nigerian bank accounts. Minimum withdrawal is ₦1,000.
                </div>
                <div class="mb-4">
                    <label for="amount" class="block text-sm font-medium text-gray-700">Amount (₦)</label>
                    <input type="number" wire:model.defer="amount" id="amount" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-black focus:ring-black sm:text-sm" step="100" min="1000" required>
                    <p class="text-xs text-gray-500 mt-1">Withdrawable balance: ₦<?php echo e(number_format($withdrawableBalance, 2)); ?></p>
                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                <div class="mb-4">
                    <label for="bank_code" class="block text-sm font-medium text-gray-700">Bank Name</label>
                    <select wire:model.defer="bank_code" id="bank_code" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-black focus:ring-black sm:text-sm" required>
                        <option value="">Select Bank</option>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $banks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bank): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($bank['code']); ?>"><?php echo e($bank['name']); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </select>
                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['bank_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                <div class="mb-4">
                    <label for="account_name" class="block text-sm font-medium text-gray-700">Account Name</label>
                    <input type="text" wire:model.defer="account_name" id="account_name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-black focus:ring-black sm:text-sm" required>
                     <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['account_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                <div class="mb-4">
                    <label for="account_number" class="block text-sm font-medium text-gray-700">Account Number</label>
                    <input type="text" wire:model.defer="account_number" id="account_number" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-black focus:ring-black sm:text-sm" required pattern="\d{10}" title="Please enter a 10-digit account number">
                     <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['account_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                <div class="flex justify-end space-x-4">
                    <button type="button" @click="show = false" class="bg-gray-200 text-gray-800 font-bold py-2 px-4 rounded hover:bg-gray-300 transition duration-300">Cancel</button>
                    <button type="submit" class="bg-black text-white font-bold py-2 px-4 rounded hover:bg-gray-800 transition duration-300">
                        <span wire:loading.remove>Request Withdrawal</span>
                        <span wire:loading>Processing...</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/vendor/earnings/index.blade.php ENDPATH**/ ?>
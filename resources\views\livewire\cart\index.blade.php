<div class="bg-white">
    <div class="max-w-2xl mx-auto pt-16 pb-24 px-4 sm:px-6 lg:max-w-7xl lg:px-8">
        <h1 class="text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl">Shopping Cart</h1>
        <div class="mt-12 lg:grid lg:grid-cols-12 lg:gap-x-12 lg:items-start xl:gap-x-16">
            <section aria-labelledby="cart-heading" class="lg:col-span-7">
                <h2 id="cart-heading" class="sr-only">Items in your shopping cart</h2>

                <ul role="list" class="border-t border-b border-gray-200 divide-y divide-gray-200">
                    @forelse ($cartItems as $item)
                        <li class="flex py-6 sm:py-10">
                            <div class="flex-shrink-0">
                                <img src="{{ $item['image_url'] ?? asset('img/default-product.png') }}" alt="{{ $item['name'] }}" class="w-24 h-24 rounded-md object-center object-cover sm:w-48 sm:h-48">
                            </div>

                            <div class="ml-4 flex-1 flex flex-col justify-between sm:ml-6">
                                <div class="relative pr-9 sm:grid sm:grid-cols-2 sm:gap-x-6 sm:pr-0">
                                    <div>
                                        <div class="flex justify-between">
                                            <h3 class="text-sm">
                                                <a href="#" class="font-medium text-gray-700 hover:text-gray-800">{{ $item['name'] }}</a>
                                            </h3>
                                        </div>
                                        <p class="mt-1 text-sm font-medium text-gray-900">@currency($item['price'])</p>
                                    </div>

                                    <div class="mt-4 sm:mt-0 sm:pr-9">
                                        <label for="quantity-{{ $item['id'] ?? '' }}" class="sr-only">Quantity, {{ $item['name'] }}</label>
                                        <input type="number" min="1" wire:model.debounce.500ms="cartItems.{{ $item['id'] ?? '' }}.quantity" wire:change="updateQuantity('{{ $item['id'] ?? '' }}', $event.target.value)" id="quantity-{{ $item['id'] ?? '' }}" name="quantity-{{ $item['id'] ?? '' }}" class="max-w-full rounded-md border border-gray-300 py-1.5 text-base leading-5 font-medium text-gray-700 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm" />
                                    </div>
                                </div>

                                <div class="mt-4 flex-1 flex items-end justify-between text-sm">
                                    <p class="flex items-center space-x-2 text-gray-700">
                                        <!-- Stock status can be added here if available -->
                                    </p>
                                    <div class="flex">
                                        <button type="button" wire:click="removeItem('{{ $item['id'] ?? '' }}')" class="font-medium text-black hover:text-gray-800">
                                            <span>Remove</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </li>
                    @empty
                        <li class="text-center py-12">
                            <p class="text-lg text-gray-500">Your cart is empty.</p>
                            <a href="{{ route('products.index') }}" class="mt-4 inline-block bg-black border border-transparent rounded-md py-3 px-8 font-medium text-white hover:bg-gray-800">Continue Shopping</a>
                        </li>
                    @endforelse
                </ul>
                <div class="mt-4">
                    @if($cartItems->isNotEmpty())
                        <button wire:click="clearCart" class="font-medium text-red-600 hover:text-red-500">Clear Cart</button>
                    @endif
                </div>
            </section>

            <!-- Order summary -->
            <section aria-labelledby="summary-heading" class="mt-16 bg-gray-50 rounded-lg px-4 py-6 sm:p-6 lg:p-8 lg:mt-0 lg:col-span-5">
                <h2 id="summary-heading" class="text-lg font-medium text-gray-900">Order summary</h2>

                <dl class="mt-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <dt class="text-sm text-gray-600">Subtotal</dt>
                        <dd class="text-sm font-medium text-gray-900">@currency($subtotal)</dd>
                    </div>
                    <!-- Shipping and tax can be added here -->
                    <div class="border-t border-gray-200 pt-4 flex items-center justify-between">
                        <dt class="text-base font-medium text-gray-900">Order total</dt>
                        <dd class="text-base font-medium text-gray-900">@currency($total)</dd>
                    </div>
                </dl>

                <div class="mt-6">
                    <a href="{{ route('checkout.index') }}" class="w-full bg-black border border-transparent rounded-md shadow-sm py-3 px-4 text-base font-medium text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-50 focus:ring-black text-center">
                        Checkout
                    </a>
                </div>
            </section>
        </div>

        <!-- Recommended products -->
        <section aria-labelledby="related-products-heading" class="mt-16">
            <h2 id="related-products-heading" class="text-lg font-medium text-gray-900">You may also like</h2>
            <div class="mt-6 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4 xl:gap-x-8">
                @foreach ($recommendedProducts as $product)
                    <div class="group relative">
                        <div class="w-full min-h-80 bg-gray-200 aspect-w-1 aspect-h-1 rounded-md overflow-hidden group-hover:opacity-75 lg:h-80 lg:aspect-none">
                            <img src="{{ $product->image_url ?? asset('img/default-product.png') }}" alt="{{ $product->name }}" class="w-full h-full object-center object-cover lg:w-full lg:h-full">
                        </div>
                        <div class="mt-4 flex justify-between">
                            <div>
                                <h3 class="text-sm text-gray-700">
                                    <a href="{{ route('products.show', $product->slug) }}">
                                        <span aria-hidden="true" class="absolute inset-0"></span>
                                        {{ $product->name }}
                                    </a>
                                </h3>
                            </div>
                            <p class="text-sm font-medium text-gray-900">@currency($product->price)</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </section>
    </div>
</div>
